{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-06-27 13:40:26,862+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-06-27 13:41:37,907+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-06-27 13:43:22,996+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/depth","Error":{"message":"Please provide valid inputs","code":-50,"s":"error"}},"timestamp":"2025-06-27 13:43:43,787+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-06-27 13:43:44,072+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/depth","Error":{"message":"Please provide valid inputs","code":-50,"s":"error"}},"timestamp":"2025-06-27 13:44:15,515+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/depth","Error":{"message":"Please provide valid inputs","code":-50,"s":"error"}},"timestamp":"2025-06-27 13:45:29,043+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:154] fyersModel","message":{"API":"/depth","error":"not a valid non-string sequence or mapping object"},"timestamp":"2025-06-27 13:46:40,997+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-06-27 16:05:08,964+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-06-29 00:41:23,479+0530","service":"FyersAPI"}
