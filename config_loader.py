"""
Configuration loader for the pivot point strategy application.
Loads configuration from config.yaml and provides centralized access to all settings.
"""

import yaml
import os
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class ConfigLoader:
    """Centralized configuration loader for the application."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the configuration loader.
        
        Args:
            config_path: Path to the config.yaml file
        """
        self.config_path = config_path
        self._config = None
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config = yaml.safe_load(file)
            
            logger.info(f"Configuration loaded successfully from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the configuration value (e.g., 'general.env_path')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        if self._config is None:
            return default
        
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    # General Settings
    @property
    def env_path(self) -> str:
        """Get environment file path."""
        return self.get('general.env_path', 'C:/Users/<USER>/Desktop/Python/.env')
    
    @property
    def output_dir(self) -> str:
        """Get output directory for reports."""
        return self.get('general.output_dir', 'reports')
    
    # Trading Symbols
    @property
    def symbols(self) -> List[str]:
        """Get list of trading symbols."""
        return self.get('symbols', ['NIFTY'])
    
    @property
    def default_symbol(self) -> str:
        """Get the first symbol as default."""
        symbols = self.symbols
        return symbols[0] if symbols else 'NIFTY'
    
    # Pivot Point Settings
    @property
    def pivot_calculation_type(self) -> str:
        """Get pivot point calculation type."""
        return self.get('pivot_point.calculation_type', 'WEEKLY')
    
    # Timeframe Settings
    @property
    def interval(self) -> int:
        """Get timeframe interval in minutes."""
        return self.get('timeframe.interval', 1)
    
    @property
    def days_to_fetch(self) -> int:
        """Get number of days of historical data to fetch."""
        return self.get('timeframe.days_to_fetch', 15)
    
    # Options Settings
    @property
    def expiry_type(self) -> str:
        """Get options expiry type."""
        return self.get('options.expiry_type', 'WEEKLY')
    
    
    
    # Options Filter Settings
    @property
    def min_volume(self) -> int:
        """Get minimum trading volume filter."""
        return self.get('options_filter.min_volume', 1000)
    
    @property
    def min_price(self) -> float:
        """Get minimum option price filter."""
        return self.get('options_filter.min_price', 2.0)
    
    @property
    def min_delta(self) -> float:
        """Get minimum delta filter."""
        return self.get('options_filter.min_delta', 0.30)
    
    @property
    def max_delta(self) -> float:
        """Get maximum delta filter."""
        return self.get('options_filter.max_delta', 0.65)
    
    # Strategy Execution Settings
    @property
    def analysis_time(self) -> str:
        """Get strategy analysis time."""
        return self.get('strategy_execution.analysis_time', '09:45')
    
    @property
    def closing_time(self) -> str:
        """Get strategy closing time."""
        return self.get('strategy_execution.closing_time', '15:06')
    
    @property
    def lot_sizes(self) -> Dict[str, int]:
        """Get lot sizes for different symbols."""
        return self.get('strategy_execution.lot_sizes', {
            'NIFTY': 50,
            'BANKNIFTY': 15,
            'FINNIFTY': 40
        })
    
    def get_lot_size(self, symbol: str) -> int:
        """
        Get lot size for a specific symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Lot size for the symbol, defaults to 50 if not found
        """
        lot_sizes = self.lot_sizes
        return lot_sizes.get(symbol.upper(), 50)
    
    # Additional utility methods
    def get_symbol_config(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive configuration for a specific symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with all relevant configuration for the symbol
        """
        return {
            'symbol': symbol,
            'min_volume': self.min_volume,
            'min_price': self.min_price,
            'min_delta': self.min_delta,
            'max_delta': self.max_delta,
            'lot_size': self.get_lot_size(symbol),
            'expiry_type': self.expiry_type,
            'pivot_calculation_type': self.pivot_calculation_type,
            'interval': self.interval,
            'days_to_fetch': self.days_to_fetch
        }
    
    def validate_config(self) -> bool:
        """
        Validate the loaded configuration.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required sections exist
            required_sections = ['general', 'symbols', 'pivot_point', 'timeframe', 
                               'options', 'options_filter', 'strategy_execution']
            
            for section in required_sections:
                if section not in self._config:
                    logger.error(f"Missing required configuration section: {section}")
                    return False
            
            # Validate symbols list is not empty
            if not self.symbols:
                logger.error("No symbols configured")
                return False
            
            # Validate delta range
            if self.min_delta >= self.max_delta:
                logger.error("min_delta must be less than max_delta")
                return False
            
            # Validate positive values
            if self.min_volume <= 0 or self.min_price <= 0:
                logger.error("min_volume and min_price must be positive")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

# Global configuration instance
_config_instance = None

def get_config(config_path: str = "config.yaml") -> ConfigLoader:
    """
    Get the global configuration instance.
    
    Args:
        config_path: Path to the config.yaml file
        
    Returns:
        ConfigLoader instance
    """
    global _config_instance
    if _config_instance is None:
        # Use absolute path if relative path is provided
        if not os.path.isabs(config_path):
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_path)
        _config_instance = ConfigLoader(config_path)
    return _config_instance

def reload_config(config_path: str = "config.yaml") -> ConfigLoader:
    """
    Reload the configuration.
    
    Args:
        config_path: Path to the config.yaml file
        
    Returns:
        New ConfigLoader instance
    """
    global _config_instance
    if not os.path.isabs(config_path):
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_path)
    _config_instance = ConfigLoader(config_path)
    return _config_instance