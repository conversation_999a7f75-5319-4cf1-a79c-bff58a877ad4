"""
Test script to verify configuration integration is working properly.
"""
import logging
from config_loader import get_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_configuration():
    """Test the configuration loading and access."""
    try:
        # Load configuration
        config = get_config()
        
        # Test validation
        if not config.validate_config():
            logger.error("Configuration validation failed!")
            return False
        
        # Test general settings
        logger.info("=== General Settings ===")
        logger.info(f"Environment path: {config.env_path}")
        logger.info(f"Output directory: {config.output_dir}")
        
        # Test symbols
        logger.info("=== Trading Symbols ===")
        logger.info(f"Configured symbols: {config.symbols}")
        logger.info(f"Default symbol: {config.default_symbol}")
        
        # Test pivot point settings
        logger.info("=== Pivot Point Settings ===")
        logger.info(f"Calculation type: {config.pivot_calculation_type}")
        
        # Test timeframe settings
        logger.info("=== Timeframe Settings ===")
        logger.info(f"Interval: {config.interval} minutes")
        logger.info(f"Days to fetch: {config.days_to_fetch}")
        
        # Test options settings
        logger.info("=== Options Settings ===")
        logger.info(f"Expiry type: {config.expiry_type}")
        logger.info(f"Monthly expiry: {config.monthly_expiry}")
        
        # Test options filter settings
        logger.info("=== Options Filter Settings ===")
        logger.info(f"Min volume: {config.min_volume}")
        logger.info(f"Min price: {config.min_price}")
        logger.info(f"Delta range: {config.min_delta} - {config.max_delta}")
        
        # Test strategy execution settings
        logger.info("=== Strategy Execution Settings ===")
        logger.info(f"Analysis time: {config.analysis_time}")
        logger.info(f"Closing time: {config.closing_time}")
        logger.info(f"Lot sizes: {config.lot_sizes}")
        
        # Test symbol-specific configuration
        logger.info("=== Symbol-Specific Configuration ===")
        for symbol in config.symbols:
            symbol_config = config.get_symbol_config(symbol)
            logger.info(f"{symbol}: {symbol_config}")
        
        logger.info("Configuration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Configuration test failed: {e}")
        return False

if __name__ == "__main__":
    test_configuration()