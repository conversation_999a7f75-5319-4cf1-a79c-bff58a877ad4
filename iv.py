import numpy as np
from scipy.stats import norm
from scipy.optimize import brentq
import math

# --- General <PERSON> Option Price for CE and PE ---
def black_scholes_price(S, K, T, r, sigma, option_type):
    sqrtT = np.sqrt(T)
    lnSK = np.log(S / K)
    d1 = (lnSK + (r + 0.5 * sigma ** 2) * T) / (sigma * sqrtT)
    d2 = d1 - sigma * sqrtT

    if option_type.upper() == 'CE':
        return S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
    elif option_type.upper() == 'PE':
        return K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
    else:
        raise ValueError("option_type must be 'CE' or 'PE'")

# --- Implied Volatility Solver ---
def implied_volatility(price, S, K, T, r, option_type):
    def objective(sigma):
        return black_scholes_price(S, K, T, r, sigma, option_type) - price
    return brentq(objective, 1e-5, 5.0)

# --- Delta Calculator (Retained Original Name) ---
def black_scholes_delta(S, K, T, r, sigma, option_type):
    if T <= 0 or sigma <= 0:
        return 0.0

    d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))

    if option_type.upper() == 'CE':
        return norm.cdf(d1)
    elif option_type.upper() == 'PE':
        return norm.cdf(d1) - 1
    else:
        raise ValueError("option_type must be 'CE' or 'PE'")

# --- Example Usage ---
if __name__ == "__main__":
    # Inputs
    S = 25637.80          # Spot price
    K = 25650             # Strike price
    option_price = 128.80 # LTP from market
    r = 0.065             # Risk-free interest rate
    T = 6 / 365           # Time to expiry (in years)
    option_type = 'PE'    # 'CE' or 'PE'

    # Step 1: Implied Volatility
    iv = implied_volatility(option_price, S, K, T, r, option_type)
    iv_percent = round(iv * 100, 2)

    # Step 2: Delta
    delta = round(black_scholes_delta(S, K, T, r, iv, option_type), 2)

    # Output
    print(f"Implied Volatility ({option_type}): {iv_percent}%")
    print(f"Delta ({option_type}): {delta}")
