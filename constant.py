"""
Constants used across the option analysis project.
These are truly constant values that don't change based on configuration.
Configuration values are now loaded from config.yaml via config_loader.py
"""

# Report settings (not configurable)
TOP_N_CLOSEST = 5

# Pivot levels (for reference) - these are standard pivot levels
PIVOT_LEVELS = ['Pivot', 'R1', 'R2', 'R3', 'R4', 'R5', 'S1', 'S2', 'S3', 'S4', 'S5']

# Date/time formats
TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"

# CSV column sets - standard columns for option data
COMMON_COLS = [
    'strike', 'expiry_date', 'type', 'symbol', 'LTP', 'volume', 'open', 'high', 'low', 'close',
    'prev_close', 'change', 'change_percent', 'delta'
]

# Fyers API interval mapping - these are API-specific constants
INTERVAL_MAP = {
    "1D": "1D",
    "1W": "1D",
    "1": "1",
    "3": "3",
    "5": "5",
    "15": "15",
    "30": "30",
    "60": "60"
}

# Log path and format - logging constants
LOG_PATH = "logs"
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Logging file handler settings
LOG_FILE_SIZE = 10 * 1024 * 1024  # 10 MB
LOG_BACKUP_COUNT = 5

