"""
Test script to connect to Fyers API and request a quote for a single option symbol.
Helps debug symbol formatting and API response issues.
"""
import logging
from fyers_connect import FyersConnect
from fyers_config import FyersConfig
from pivot_points import _calculate_pivot_standard
from config_loader import get_config

# Set up logging to print to console
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fyers_test")

# Load configuration and initialize FyersConnect
config = get_config()
fyers_config = FyersConfig(env_path=config.env_path)
fyers = FyersConnect(fyers_config)

# Authenticate
if not fyers.login():
    logger.error("Fyers login failed. Check credentials and authentication flow.")
    exit(1)

# --- Symbol Quote Request ---
# Test with a known-valid option symbol (update expiry/strike as needed)
test_symbol = "BSE:SENSEX-INDEX"  # Example: NIFTY, expiry 2025-06-26, strike 25000, CE

logger.info(f"Requesting quote for symbol: {test_symbol}")

try:
    response = fyers.fyers.quotes({"symbols": test_symbol})
    #data = {"symbol": test_symbol,"ohlcv_flag":"1"}
    #response = fyers.fyers.depth(data=data)
    print("API response:", response)
except Exception as e:
    logger.error(f"Error requesting quote: {e}")

# --- Weekly Pivot Point Calculation Test ---
# Test with a known-valid option symbol (update expiry/strike as needed)
test_symbol = "NSE:BANKNIFTY25JUL56400CE"  # Example: NIFTY, expiry 2025-06-26, strike 24900, CE

logger.info(f"Testing weekly pivot point calculation for symbol: {test_symbol}")

try:
   
    data = {
                "symbol": test_symbol,
                "resolution": "1D",  # Daily candles
                "date_format": "1",
                "range_from": "2025-06-16",
                "range_to": "2025-06-20",
                "cont_flag": "1"
            }
    response = fyers.fyers.history(data)
    print(response)
    """
    weekly_ohlc = fyers._fetch_weekly_pivot_ohlc_data(test_symbol)
    print("Weekly OHLC fetched:", weekly_ohlc)
    if not weekly_ohlc or not all(k in weekly_ohlc for k in ['high', 'low', 'close']):
        logger.warning(f"No valid OHLC data for {test_symbol}. Cannot calculate pivots.")
    else:
        pivots = _calculate_pivot_standard(weekly_ohlc['high'], weekly_ohlc['low'], weekly_ohlc['close'])
        logger.info(f"{test_symbol} Weekly Pivot Levels: " + ", ".join([f"{k}: {v}" for k, v in pivots.items()]))
        print(f"{test_symbol} Weekly Pivot Levels: " + ", ".join([f"{k}: {v}" for k, v in pivots.items()]))
    """
except Exception as e:
    logger.error(f"Error fetching weekly OHLC or calculating pivots: {e}")

# --- Investigation: Why do we get all zero pivots? ---
# If you see all zeros, it means _fetch_weekly_pivot_ohlc_data returned 0s for high/low/close.
# This usually happens because Fyers API does not provide OHLC data for option symbols, only for underlying indices/stocks.
# Check the logs for 'No data returned for ...' or 'No valid OHLC data ...' to confirm this.
# If you want valid pivots, use the underlying index (e.g., 'NSE:NIFTY') instead of the option symbol.
