"""
Module for calculating pivot points and support/resistance levels.
"""
import logging
from typing import Dict, Any
from fyers_connect import FyersConnect
from datetime import datetime, timedelta
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def _calculate_pivot_standard(high: float, low: float, close: float) -> Dict[str, float]:
    """
    Calculates the Standard Pivot Point and support/resistance levels using the extended formulas provided by user.

    Parameters:
    - high: float - High price
    - low: float - Low price
    - close: float - Close price

    Returns:
    A dictionary with the pivot point (P), resistance levels (R1, R2, R3, R4, R5),
    and support levels (S1, S2, S3, S4, S5)
    """
    def _align_to_nearest_5(x: float) -> float:
        """
        Rounds to the nearest value ending with .00 or .05 (up or down as per closest).
        E.g., 44.03->44.05, 69.97->69.95, 17.92->17.90, 96.08->96.10, -8.02->-8.00, 122.02->122.00, -34.13->-34.15, 147.95->147.95, -60.25->-60.25, 173.88->173.90, -86.37->-86.35
        """
        sign = 1 if x >= 0 else -1
        abs_x = abs(x)
        int_part = int(abs_x)
        decimal = abs_x - int_part
        # Find the nearest .00 or .05
        lower = int_part + (0.00 if decimal < 0.025 else 0.05)
        upper = int_part + (0.05 if decimal < 0.05 else 0.10)
        # Find the two candidates
        candidate1 = int_part + (round(decimal / 0.05) * 0.05)
        candidate2 = int_part + (int(decimal / 0.05) * 0.05)
        # Always round to nearest .00 or .05
        nearest = round(abs_x * 20) / 20.0  # 0.05 steps
        # Now, round to 2 decimals and restore sign
        return round(sign * nearest, 2)

    # Calculate pivot point
    P = (high + low + close) / 3

    # Resistance and support levels using extended formulas
    R1 = P * 2 - low
    S1 = P * 2 - high
    R2 = P + (high - low)
    S2 = P - (high - low)
    R3 = P * 2 + (high - 2 * low)
    S3 = P * 2 - (2 * high - low)
    R4 = P * 3 + (high - 3 * low)
    S4 = P * 3 - (3 * high - low)
    R5 = P * 4 + (high - 4 * low)
    S5 = P * 4 - (4 * high - low)

    return {
        'Pivot': _align_to_nearest_5(round(P, 2)),
        'R1': _align_to_nearest_5(round(R1, 2)),
        'S1': _align_to_nearest_5(round(S1, 2)),
        'R2': _align_to_nearest_5(round(R2, 2)),
        'S2': _align_to_nearest_5(round(S2, 2)),
        'R3': _align_to_nearest_5(round(R3, 2)),
        'S3': _align_to_nearest_5(round(S3, 2)),
        'R4': _align_to_nearest_5(round(R4, 2)),
        'S4': _align_to_nearest_5(round(S4, 2)),
        'R5': _align_to_nearest_5(round(R5, 2)),
        'S5': _align_to_nearest_5(round(S5, 2)),
    }

def calculate_pivot_points(fyers_connect: FyersConnect, symbol: str, period: str) -> Dict[str, float]:
    """
    Calculates pivot points based on the specified period (DAILY, WEEKLY, MONTHLY).

    Parameters:
    - fyers_connect: An instance of FyersConnect for fetching data.
    - symbol: The trading symbol (e.g., "NIFTY").
    - period: The calculation period ("DAILY", "WEEKLY", "MONTHLY").

    Returns:
    A dictionary with the pivot point and support/resistance levels, or an empty dictionary if data is insufficient.
    """
    logger.info(f"Calculating {period} pivot points for {symbol}")
    today = datetime.now()
    ohlc_data = pd.DataFrame()

    if period == "DAILY":
        # For daily pivot, we need previous day's OHLC
        start_date = (today - timedelta(days=1)).strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        ohlc_data = fyers_connect.get_ohlc_data(symbol, interval="1D", start_date=start_date, end_date=end_date)
        if not ohlc_data.empty:
            # Use the last available complete day's data
            high = ohlc_data["high"].iloc[-1]
            low = ohlc_data["low"].iloc[-1]
            close = ohlc_data["close"].iloc[-1]
            return _calculate_pivot_standard(high, low, close)
        else:
            logger.warning(f"Could not fetch daily OHLC data for {symbol} for {start_date}.")
            return {}

    elif period == "WEEKLY":
        # For weekly pivot, we need previous week's OHLC
        # This method already handles fetching and aggregating daily data for the previous week
        ohlc_data = fyers_connect.get_weekly_pivot_ohlc_data(symbol)
        if not ohlc_data.empty:
            high = ohlc_data["high"].iloc[-1]
            low = ohlc_data["low"].iloc[-1]
            close = ohlc_data["close"].iloc[-1]
            return _calculate_pivot_standard(high, low, close)
        else:
            logger.warning(f"Could not fetch weekly OHLC data for {symbol}.")
            return {}

    elif period == "MONTHLY":
        # For monthly pivot, we need previous month's OHLC
        # Fetch daily data for the entire previous month and aggregate
        first_day_of_current_month = today.replace(day=1)
        last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
        first_day_of_previous_month = last_day_of_previous_month.replace(day=1)

        start_date_str = first_day_of_previous_month.strftime("%Y-%m-%d")
        end_date_str = last_day_of_previous_month.strftime("%Y-%m-%d")

        logger.info(f"Fetching daily data for monthly pivot from {start_date_str} to {end_date_str}")
        ohlc_data = fyers_connect.get_ohlc_data(symbol, interval="1D", start_date=start_date_str, end_date=end_date_str)

        if not ohlc_data.empty:
            high = ohlc_data["high"].max()
            low = ohlc_data["low"].min()
            open_price = ohlc_data["open"].iloc[0]
            close = ohlc_data["close"].iloc[-1]
            return _calculate_pivot_standard(high, low, close)
        else:
            logger.warning(f"Could not fetch monthly OHLC data for {symbol} for {start_date_str} to {end_date_str}.")
            return {}

    else:
        logger.error(f"Unsupported pivot period: {period}")
        return {}