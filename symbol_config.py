"""
Configuration settings for different trading symbols.
"""

# Symbol-specific configuration
SYMBOL_CONFIG = {
    # Index configurations
    "NIFTY": {
        "strike_interval": 50,  # Strike prices increment by 50 points
        "distance_threshold_pct": 0.5,  # 0.5% distance from pivot
        "min_volume": 1000,
        "min_price": 2.0,
        "target_delta": 0.5
    },
    "BANKNIFTY": {
        "strike_interval": 100,  # Strike prices increment by 100 points
        "distance_threshold_pct": 0.75,  # 0.75% distance from pivot (increased for wider intervals)
        "min_volume": 500,  # Adjusted for potentially lower volume
        "min_price": 5.0,  # Adjusted for typically higher premiums
        "target_delta": 0.5
    },
    "FINNIFTY": {
        "strike_interval": 50,  # Strike prices increment by 50 points
        "distance_threshold_pct": 0.5,
        "min_volume": 500,
        "min_price": 2.0,
        "target_delta": 0.5
    },

    # Stock configurations
    "RELIANCE": {
        "strike_interval": 20,  # Strike prices increment by 20 points
        "distance_threshold_pct": 1.0,  # 1.0% distance from pivot (stocks can be more volatile)
        "min_volume": 100,  # Lower volume for stock options
        "min_price": 1.0,  # Lower minimum price for stock options
        "target_delta": 0.5
    },
    "HDFCBANK": {
        "strike_interval": 20,
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5
    },
    "INFY": {
        "strike_interval": 20,
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5
    },
    "TCS": {
        "strike_interval": 20,
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5
    },
    "WIPRO": {
        "strike_interval": 10,  # Smaller strike interval for lower-priced stocks
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 0.5,
        "target_delta": 0.5
    },

    # Default configuration for any other symbol
    "DEFAULT": {
        "strike_interval": 20,  # Default to 20-point intervals for stocks
        "distance_threshold_pct": 1.0,  # Default to 1% for stocks
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5
    }
}

def get_symbol_config(symbol):
    """
    Get configuration for a specific symbol.

    Parameters:
        symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')

    Returns:
        Dictionary with configuration values for the symbol
    """
    # Convert symbol to uppercase for case-insensitive matching
    symbol = symbol.upper()

    # Return symbol-specific config if available, otherwise return default
    return SYMBOL_CONFIG.get(symbol, SYMBOL_CONFIG["DEFAULT"])
