"""
Module for Fyers API integration.
Handles authentication, data fetching, and market data operations.
"""
import os
import json
import logging
import time
import pytz
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

import pandas as pd
from fyers_apiv3 import fyersModel
from fyers_apiv3.FyersWebsocket.data_ws import FyersDataSocket
from config_loader import get_config

# Configure logging
logger = logging.getLogger(__name__)

class FyersConnect:
    # Mapping of intervals to Fyers API resolutions
    INTERVAL_MAP = {
        "1D": "1D",   # Daily candle
        "1W": "1D",   # For weekly, we'll fetch daily data and aggregate
        "1": "1",     # 1 minute
        "3": "3",     # 3 minutes
        "5": "5",     # 5 minutes
        "15": "15",   # 15 minutes
        "30": "30",   # 30 minutes
        "60": "60"    # 1 hour
    }

    def __init__(self, fyers_config):
        """
        Initialize Fyers API connection.

        Args:
            fyers_config: An instance of FyersConfig.
        """
        self.fyers_auth = fyers_config
        self.client_id = self.fyers_auth.config['client_id']
        self.log_path = 'logs'
        self.access_token = None
        self.fyers = None
        self.ws = None

    def login(self) -> bool:
        """
        Authenticate with Fyers API using integrated authentication system.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Starting Fyers authentication process...")
            
            access_token = self.fyers_auth.authenticate()
            if not access_token:
                logger.error("Authentication failed")
                return False
            
            self.access_token = access_token
            
            # Initialize Fyers model
            self.fyers = fyersModel.FyersModel(
                client_id=self.client_id,
                token=self.access_token,
                log_path=self.log_path
            )
            
            # Verify connection
            profile = self.fyers.get_profile()
            if profile and profile.get("code") == 200:
                logger.info("Successfully logged in to Fyers API")
                return True
            else:
                logger.error(f"Failed to verify connection: {profile}")
                return False
                
        except Exception as e:
            logger.error(f"Error during Fyers login: {str(e)}")
            return False

    def get_ohlc_data(self, symbol: str, interval: str = "1D",
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Fetch OHLC data for a symbol.

        Args:
            symbol: Trading symbol
            interval: Time interval (1D, 1W, etc.)
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            pd.DataFrame: OHLC data
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbol for Fyers
            fyers_symbol = self._format_symbol(symbol)

            # Set default dates if not provided
            if not start_date:
                today = datetime.now()
                if interval == "1W":
                    logger.info(f"Delegating weekly OHLC data fetch for {symbol} to get_weekly_pivot_ohlc_data.")
                    return self.get_weekly_pivot_ohlc_data(symbol)
                else:
                    # For other intervals, use configured days_to_fetch
                    config = get_config()
                    days_to_fetch = config.days_to_fetch
                    start_date = (today - timedelta(days=days_to_fetch)).strftime("%Y-%m-%d")
                    end_date = today.strftime("%Y-%m-%d")

            # For non-weekly intervals or when specific dates are provided
            data = {
                "symbol": fyers_symbol,
                "resolution": self.INTERVAL_MAP[interval],
                "date_format": "1",
                "range_from": start_date,
                "range_to": end_date or start_date,
                "cont_flag": "1"
            }

            response = self.fyers.history(data)
            if response["code"] != 200:
                raise Exception(f"Failed to fetch OHLC data: {response}")

            df = pd.DataFrame(response["candles"],
                            columns=["timestamp", "open", "high", "low", "close", "volume"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
            df.set_index("timestamp", inplace=True)
            return df

        except Exception as e:
            logger.error(f"Error fetching OHLC data: {str(e)}")
            return pd.DataFrame()

    def get_weekly_pivot_ohlc_data(self, symbol: str) -> pd.DataFrame:
        """
        Fetch OHLC data for the previous week to calculate weekly pivot points.
        This method fetches daily data for the previous week and aggregates it
        into a single OHLC candle representing the previous week's trading.

        Args:
            symbol: Trading symbol (e.g., "NIFTY")

        Returns:
            pd.DataFrame: A DataFrame with a single row containing the previous week's
                          OHLC data, or an empty DataFrame if data cannot be fetched.
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            today = datetime.now()
            # Calculate the start and end of the previous week
            # Assuming week starts on Monday (0) and ends on Sunday (6)
            # We need data up to last Friday for weekly pivot calculation
            
            # Go back to the most recent Sunday
            days_since_sunday = today.weekday() + 1
            last_sunday = today - timedelta(days=days_since_sunday)

            # Previous week's Monday
            prev_week_monday = last_sunday - timedelta(days=6)
            # Previous week's Friday (last trading day)
            prev_week_friday = last_sunday - timedelta(days=2) # Sunday - 2 days = Friday

            start_date_str = prev_week_monday.strftime("%Y-%m-%d")
            end_date_str = prev_week_friday.strftime("%Y-%m-%d")

            logger.info(f"Fetching daily data for weekly pivot from {start_date_str} to {end_date_str}")
            daily_data_df = self._fetch_weekly_pivot_ohlc_data(symbol, start_date_str, end_date_str)

            if daily_data_df.empty:
                logger.warning(f"No daily data found for {symbol} between {start_date_str} and {end_date_str}")
                return pd.DataFrame()

            # Aggregate daily data to form a single weekly candle
            weekly_open = daily_data_df["open"].iloc[0]
            weekly_high = daily_data_df["high"].max()
            weekly_low = daily_data_df["low"].min()
            weekly_close = daily_data_df["close"].iloc[-1]
            weekly_volume = daily_data_df["volume"].sum()

            weekly_ohlc = pd.DataFrame([{
                "timestamp": prev_week_friday, # Use Friday's date as the timestamp for the weekly candle
                "open": weekly_open,
                "high": weekly_high,
                "low": weekly_low,
                "close": weekly_close,
                "volume": weekly_volume
            }])
            weekly_ohlc["timestamp"] = pd.to_datetime(weekly_ohlc["timestamp"])
            weekly_ohlc.set_index("timestamp", inplace=True)
            logger.info(f"Successfully aggregated weekly OHLC data for {symbol}")
            return weekly_ohlc

        except Exception as e:
            logger.error(f"Error fetching weekly pivot OHLC data for {symbol}: {str(e)}")
            return pd.DataFrame()

    def _fetch_weekly_ohlc_for_option(self, symbol: str) -> Dict[str, float]:
        """
        Helper method to fetch OHLC data for the previous week for a specific option symbol.
        This method fetches daily data for the previous week and aggregates it
        into a single OHLC candle representing the previous week's trading.

        Args:
            symbol: Option trading symbol (e.g., "NSE:NIFTY2570325500PE")

        Returns:
            Dict: A dictionary with 'open', 'high', 'low', 'close' for the previous week,
                  or an empty dictionary if data cannot be fetched.
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            today = datetime.now()
            # Calculate the start and end of the previous week
            # Assuming week starts on Monday (0) and ends on Sunday (6)
            # We need data up to last Friday for weekly pivot calculation
            
            # Go back to the most recent Sunday
            days_since_sunday = today.weekday() + 1
            last_sunday = today - timedelta(days=days_since_sunday)

            # Previous week's Monday
            prev_week_monday = last_sunday - timedelta(days=6)
            # Previous week's Friday (last trading day)
            prev_week_friday = last_sunday - timedelta(days=2) # Sunday - 2 days = Friday

            start_date_str = prev_week_monday.strftime("%Y-%m-%d")
            end_date_str = prev_week_friday.strftime("%Y-%m-%d")

            logger.info(f"Fetching daily data for weekly OHLC for option {symbol} from {start_date_str} to {end_date_str}")
            daily_data_df = self._fetch_daily_ohlc_data_for_range(symbol, start_date_str, end_date_str)

            if daily_data_df.empty:
                logger.warning(f"No daily data found for option {symbol} between {start_date_str} and {end_date_str}")
                return {}

            # Aggregate daily data to form a single weekly candle
            weekly_open = daily_data_df["open"].iloc[0]
            weekly_high = daily_data_df["high"].max()
            weekly_low = daily_data_df["low"].min()
            weekly_close = daily_data_df["close"].iloc[-1]

            return {
                "open": weekly_open,
                "high": weekly_high,
                "low": weekly_low,
                "close": weekly_close
            }

        except Exception as e:
            logger.error(f"Error fetching weekly OHLC data for option {symbol}: {str(e)}")
            return {}

    def _fetch_daily_ohlc_data_for_range(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Helper method to fetch daily OHLC data for a given date range.

        Args:
            symbol: Trading symbol
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            pd.DataFrame: OHLC data for the specified range, or empty DataFrame.
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            fyers_symbol = self._format_symbol(symbol)
            data = {
                "symbol": fyers_symbol,
                "resolution": "1D",  # Always fetch daily for aggregation
                "date_format": "1",
                "range_from": start_date,
                "range_to": end_date,
                "cont_flag": "1"
            }

            response = self.fyers.history(data)

            # Handle "no data" response gracefully
            if response.get("s") == "no_data":
                logger.warning(f"No historical data available for {fyers_symbol} in the given range.")
                return pd.DataFrame()

            if response.get("code") != 200:
                raise Exception(f"Failed to fetch daily OHLC data for range: {response}")

            df = pd.DataFrame(response["candles"],
                            columns=["timestamp", "open", "high", "low", "close", "volume"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
            df.set_index("timestamp", inplace=True)
            df.sort_index(inplace=True) # Ensure data is sorted by timestamp
            return df

        except Exception as e:
            logger.error(f"Error in _fetch_daily_ohlc_data_for_range: {str(e)}")
            return pd.DataFrame()

    def get_option_chain(self, symbol: str, expiry_type: str = "weekly") -> List[Dict[str, Any]]:
        """
        Fetch option chain data for a symbol.

        Args:
            symbol: Trading symbol
            expiry_type: Type of expiry to fetch ('weekly' or 'monthly')

        Returns:
            List[Dict]: Option chain data
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbol for spot price
            fyers_symbol = self._format_symbol(symbol)

            # Get expiry dates based on expiry_type
            today = datetime.now()
            expiry_dates = []

            if expiry_type.upper() == "WEEKLY":
                # Calculate next Thursday expiry for weekly
                days_to_thursday = (3 - today.weekday()) % 7  # 3 is Thursday
                if days_to_thursday == 0 and today.hour >= 15:  # After market hours on Thursday
                    days_to_thursday = 7  # Use next Thursday

                nearest_expiry = (today + timedelta(days=days_to_thursday)).date()
                expiry_dates.append(nearest_expiry)
                logger.info(f"Using weekly expiry date: {nearest_expiry.strftime('%Y-%m-%d')}")
            else:
                # For monthly expiry, find the last Thursday of current and/or next month
                from option_utils import get_last_thursday_of_month
                current_date = today.date()
                current_month = current_date.month
                current_year = current_date.year

                # Get the last Thursday of the current month
                last_thursday_current = get_last_thursday_of_month(current_year, current_month)

                # If the current month's expiry has already passed, use next month's expiry
                if current_date > last_thursday_current:
                    next_month = (current_month % 12) + 1
                    next_year = current_year + (1 if current_month == 12 else 0)
                    last_thursday_next = get_last_thursday_of_month(next_year, next_month)
                    expiry_dates.append(last_thursday_next)
                    logger.info(f"Current month expiry has passed. Using next month's expiry: {last_thursday_next.strftime('%Y-%m-%d')}")
                else:
                    expiry_dates.append(last_thursday_current)
                    logger.info(f"Using current month's expiry: {last_thursday_current.strftime('%Y-%m-%d')}")

            # Use the first expiry date for initial processing
            nearest_expiry = expiry_dates[0]
            logger.info(f"Using primary expiry date: {nearest_expiry.strftime('%Y-%m-%d')}")

            # Get spot price
            spot_response = self.fyers.quotes({"symbols": fyers_symbol})
            if spot_response["code"] != 200:
                raise Exception(f"Failed to fetch spot price: {spot_response}")

            spot_price = 0.0
            previous_close = 0.0
            if spot_response.get("d") and len(spot_response["d"]) > 0:
                spot_data = spot_response["d"][0]
                if "v" in spot_data:
                    spot_values = spot_data["v"]
                    spot_price = float(spot_values.get("lp", 0.0))
                    previous_close = float(spot_values.get("prev_close_price", 0.0))
                    logger.info(f"Spot price: {spot_price}")

            # Get strike interval from config
            from symbol_config import get_symbol_config
            symbol_config = get_symbol_config(symbol)
            strike_interval = symbol_config.get("strike_interval", 50)

            # Calculate strikes
            base_strike = round(spot_price / strike_interval) * strike_interval
            num_strikes = 10
            potential_strikes = [
                base_strike + (i - num_strikes) * strike_interval
                for i in range(num_strikes * 2 + 1)
            ]

            # Build option symbols list
            symbols_for_quote = []
            year = nearest_expiry.strftime('%y')
            month = str(nearest_expiry.month)  # Single digit month
            day = nearest_expiry.strftime('%d')

            # Import the option symbol formatter
            from option_utils import format_weekly_option_symbol, format_monthly_option_symbol
            
            for strike in potential_strikes:
                # Use the correct format for Fyers API: NSE:NIFTYYYMDD19000CE
                if expiry_type.upper() == "WEEKLY":
                    ce_symbol = format_weekly_option_symbol(self, symbol, nearest_expiry, int(strike), "CE")
                    pe_symbol = format_weekly_option_symbol(self, symbol, nearest_expiry, int(strike), "PE")
                else:
                    ce_symbol = format_monthly_option_symbol(self, symbol, nearest_expiry, int(strike), "CE")
                    pe_symbol = format_monthly_option_symbol(self, symbol, nearest_expiry, int(strike), "PE")
                
                # Add symbols to the list
                symbols_for_quote.extend([ce_symbol, pe_symbol])
                
                logger.info(f"Added option symbols for strike {strike}: CE={ce_symbol}, PE={pe_symbol}")

            # Process in batches
            processed_data = []
            for i in range(0, len(symbols_for_quote), 50):
                batch = symbols_for_quote[i:i + 50]
                options_response = self.fyers.quotes({"symbols": self._format_batch_symbols(batch)})

                logger.info(f"Fetching quotes for batch of {len(batch)} symbols")

                if not isinstance(options_response, dict) or options_response.get("code") != 200:
                    logger.error(f"Invalid response from Fyers API for batch: {options_response}")
                    logger.error(f"Batch contained {len(batch)} symbols")
                    # Log the first few symbols in the batch for debugging
                    if batch:
                        logger.error(f"First few symbols in batch: {', '.join(batch[:min(5, len(batch))])}")
                    continue

                quote_data = options_response.get("d", [])
                if not quote_data:
                    logger.error(f"No quote data returned from Fyers API for batch of {len(batch)} symbols")
                    continue

                for quote in quote_data:
                    if not isinstance(quote, dict):
                        continue

                    symbol_name = quote.get("n", "")
                    if not symbol_name:
                        continue

                    # Clean up the symbol name - remove any extra characters
                    symbol_name = symbol_name.strip()
                    # Remove any unexpected characters like quotes, brackets, etc.
                    symbol_name = re.sub(r"[\[\]']", "", symbol_name)
                    # Remove any trailing characters that aren't part of the symbol
                    symbol_name = re.sub(r"(NSE:[A-Z0-9]+(?:CE|PE)).*", r"\1", symbol_name)

                    logger.info(f"Processing symbol: {symbol_name}")

                    # Parse NSE:NIFTY2570325050CE format
                    try:
                        # Split into exchange and symbol parts
                        parts = symbol_name.split(":")
                        if len(parts) != 2:
                            logger.warning(f"Invalid symbol format (no exchange separator): {symbol_name}")
                            continue

                        option_part = parts[1].strip()  # NIFTY2570325050CE or NIFTY25JUL57000CE

                        # Choose regex based on expiry type
                        if expiry_type.upper() == "WEEKLY":
                            match = re.match(r"([A-Z]+)(\d{2})(0?[1-9]|1[0-2])(\d{2})(\d+)(CE|PE)", option_part)
                            if not match:
                                logger.warning(f"Could not parse weekly symbol '{symbol_name}': No match found")
                                continue
                            base_symbol, year, month, day, strike_str, opt_type = match.groups()
                        else:  # MONTHLY
                            match = re.match(r"([A-Z]+)(\d{2})([A-Z]{3})(\d+)(CE|PE)", option_part)
                            if not match:
                                logger.warning(f"Could not parse monthly symbol '{symbol_name}': No match found")
                                continue
                            base_symbol, year, month, strike_str, opt_type = match.groups()
                            day = None  # Day is not part of the monthly symbol format

                        strike = float(strike_str)

                        log_day = day if day is not None else "N/A"
                        logger.info(f"Successfully parsed: base={base_symbol}, year={year}, month={month}, "
                                   f"day={log_day}, strike={strike}, type={opt_type}")

                    except Exception as e:
                        logger.warning(f"Could not parse symbol '{symbol_name}': {e}")
                        continue

                    is_call = opt_type == 'CE'
                    option_values = quote.get("v", {})

                    # Log the full option_values for debugging
                    logger.debug(f"Raw option_values for {symbol_name}: {option_values}")

                    # First try to get values from main response
                    ltp = float(option_values.get('lp', 0.0))
                    prev_close = float(option_values.get('previousClose', option_values.get('prev_close_price', 0.0)))

                    # If values are missing or we got an error, try fallback symbol
                    if (ltp == 0.0 or prev_close == 0.0) and (option_values.get('code', 0) == -300 or 'errmsg' in option_values):
                        logger.error(f"Weekly option symbol {symbol_name} is invalid or returned no data. Please set 'expiry_type' to 'MONTHLY' in your config.yaml and rerun. If already using MONTHLY, Symbol issue check with broker.")
                        raise ValueError("Weekly option symbol is invalid. Please update config.yaml to use MONTHLY expiry. If already using MONTHLY, Symbol issue check with broker.")

                    # Calculate change and change percentage
                    # Use prev_close as previous close, but if it's zero and fallback_values has prevClose, use that
                    change = 0.0
                    change_percent = 0.0
                    """
                    # Try to get a valid previous close from any available field
                    if prev_close == 0.0 and 'fallback_values' in locals():
                        fallback_prev_close = fallback_values.get('prevClose')
                        if fallback_prev_close:
                            try:
                                prev_close = float(fallback_prev_close)
                            except Exception:
                                pass
                    if prev_close > 0 and ltp > 0:
                        change = ltp - prev_close
                        change_percent = (change / prev_close) * 100.0
                        logger.info(f"Calculated changes for {symbol_name}: Change={change:.2f}, Change%={change_percent:.2f}%")
                    """
                    # Use our calculated values, fallback to API values if available
                    ch = float(option_values.get('ch', change))
                    chp = float(option_values.get('chp', change_percent))
                
                    # Calculate time to expiry for reference
                    time_to_expiry = self._calculate_time_to_expiry(nearest_expiry)
                    
                    # Use Black-Scholes model for delta calculation
                    # S = spot_price, K = strike, T = time_to_expiry, r = risk-free rate, sigma = volatility
                    # Placeholder values for r and sigma as they are not available from Fyers API
                    # r (risk-free rate) = 5% (0.05)
                    # sigma (implied volatility) = 20% (0.2)
                    
                    # Import black_scholes_delta from option_utils
                    from option_utils import black_scholes_delta, implied_volatility
                    
                    # Default values for risk-free rate and implied volatility
                    risk_free_rate = 0.065  # 5%
                    
                    #implied_volatility = 0.20 # 20%
                    iv = implied_volatility(
                        ltp, 
                        S=spot_price,
                        K=strike,
                        T=time_to_expiry,
                        r=risk_free_rate, 
                        option_type=opt_type
                    )
                    delta = black_scholes_delta(
                        S=spot_price,
                        K=strike,
                        T=time_to_expiry,
                        r=risk_free_rate,
                        sigma=iv,
                        option_type=opt_type
                    )

                    # Find or create record for this strike
                    record = next((item for item in processed_data if item["strike"] == strike), None)
                    if record is None:
                        record = {
                            'strike': strike,
                            'expiry_date': nearest_expiry,
                            'call_symbol': "",
                            'put_symbol': "",
                            'call_price': 0.0,
                            'put_price': 0.0,
                            'call_volume': 0,
                            'put_volume': 0,
                            'call_oi': 0,
                            'put_oi': 0,
                            'call_iv': 0.0,
                            'put_iv': 0.0,
                            'call_change': 0.0,
                            'put_change': 0.0,
                            'call_delta': 0.0,  # Add delta field
                            'put_delta': 0.0,   # Add delta field
                            'spot_price': spot_price,
                            'time_to_expiry': time_to_expiry
                        }
                        processed_data.append(record)

                    # Get actual volume, OI, prev_close_price, and OHLC from API, don't simulate
                    # Use new keys if present, else fallback to old keys
                    volume = int(option_values.get('volume', option_values.get('v', 0)))
                    oi = int(option_values.get('oi', 0))
                    prev_close_price = float(option_values.get('prev_close_price', 0.0))
                    open_price = float(option_values.get('open_price', option_values.get('o', 0.0)))
                    high_price = float(option_values.get('high_price', option_values.get('h', 0.0)))
                    low_price = float(option_values.get('low_price', option_values.get('l', 0.0)))
                    close_price = float(option_values.get('close_price', option_values.get('c', 0.0)))

                    # Enhanced logging for debugging zero OHLCV/volume
                    if (volume == 0 and open_price == 0 and high_price == 0 and low_price == 0 and close_price == 0):
                        logger.error(f"API returned all zero OHLCV/volume for {symbol_name} (option_values={option_values})")
                        # Optionally, skip this record
                        # continue  # Do not continue here, fallback may provide valid OHLCV

                    # Separate logic for weekly and monthly expiry
                    if expiry_type.upper() == "WEEKLY":
                        if ltp == 0.0 and volume == 0:
                            logger.error(f"Weekly option symbol {symbol_name} is invalid or returned no data. Please set 'expiry_type' to 'MONTHLY' in your config.yaml and rerun.")
                            raise ValueError("Weekly option symbol is invalid. Please update config.yaml to use MONTHLY expiry.")
                    else:
                        if ltp == 0.0 and volume == 0:
                            logger.warning(f"Zero data for symbol {symbol_name}: option_values={option_values}")

                    # After fallback, skip if still all zero
                    if (volume == 0 and open_price == 0 and high_price == 0 and low_price == 0 and close_price == 0):
                        continue

                    # Update record with quote data
                    if is_call:
                        record['call_symbol'] = symbol_name
                        record['call_price'] = ltp
                        record['call_volume'] = volume
                        record['call_oi'] = oi
                        record['call_iv'] = float(option_values.get('iv', 0.2))  # Default IV of 20%
                        record['call_change'] = ch
                        record['call_change_percent'] = chp
                        record['call_delta'] = delta  # Add delta field
                        record['call_prev_close'] = prev_close_price
                        record['call_open'] = open_price
                        record['call_high'] = high_price
                        record['call_low'] = low_price
                        record['call_close'] = close_price
                    else:
                        record['put_symbol'] = symbol_name
                        record['put_price'] = ltp
                        record['put_volume'] = volume
                        record['put_oi'] = oi
                        record['put_iv'] = float(option_values.get('iv', 0.2))  # Default IV of 20%
                        record['put_change'] = ch
                        record['put_change_percent'] = chp
                        record['put_delta'] = delta  # Add delta field
                        record['put_prev_close'] = prev_close_price
                        record['put_open'] = open_price
                        record['put_high'] = high_price
                        record['put_low'] = low_price
                        record['put_close'] = close_price

                    logger.info(f"Processed {opt_type} option: Strike={strike} - "
                              f"LTP: {ltp:.2f}, "
                              f"Volume: {volume}, "
                              f"Change: {ch:.2f}, "
                              f"Change%: {chp:.2f}")

            # Sort by strike price
            processed_data.sort(key=lambda x: x['strike'])
            logger.info(f"Successfully processed {len(processed_data)} option strikes")
            return processed_data

        except Exception as e:
            if isinstance(e, ValueError):
                raise  # Re-raise the ValueError to be caught by the caller
            logger.error(f"Error fetching option chain: {str(e)}")
            return []

    def _is_weekly_expiry(self, expiry_date: str) -> bool:
        """
        Check if an expiry date is a weekly expiry.

        Args:
            expiry_date: Expiry date string from Fyers API

        Returns:
            bool: True if weekly expiry, False otherwise
        """
        try:
            from datetime import datetime
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
            # If it's a Thursday and not the last Thursday of the month
            return expiry.weekday() == 3 and expiry.day < 22
        except Exception:
            return False

    def _get_exchange_for_symbol(self, symbol_name: str) -> str:
        """
        Determines the exchange for a given symbol.
        """
        if symbol_name.upper() == "SENSEX":
            return "BSE"
        return "NSE"

    def _format_symbol(self, symbol: str) -> str:
        """
        Format symbol for Fyers API.

        Args:
            symbol: Trading symbol

        Returns:
            str: Formatted symbol
        """
        # Add symbol formatting logic based on Fyers requirements
        # Check if the symbol is already in Fyers option format (e.g., NSE:NIFTY2570325200CE)
        if symbol.startswith("NSE:") or symbol.startswith("BSE:") and ("CE" in symbol or "PE" in symbol):
            return symbol

        exchange = self._get_exchange_for_symbol(symbol)

        if symbol.upper() == "NIFTY":
            return f"{exchange}:NIFTY50-INDEX"
        elif symbol.upper() == "BANKNIFTY":
            return f"{exchange}:NIFTYBANK-INDEX"
        elif symbol.upper() == "FINNIFTY":
            return f"{exchange}:FINNIFTY-INDEX"
        elif symbol.upper() == "SENSEX":
            return f"{exchange}:SENSEX-INDEX"
        else:
            return f"{exchange}:{symbol.upper()}-EQ"

    def _format_batch_symbols(self, batch):
        """
        Format a list of symbols into a comma-separated string for Fyers API.
        Args:
            batch (list): List of symbol strings
        Returns:
            str: Comma-separated symbols string
        """
        return ','.join(batch)

    def _calculate_time_to_expiry(self, expiry_date) -> float:
        """
        Calculate time to expiry in years.
        """
        if isinstance(expiry_date, str):
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
        else:
            expiry = datetime.combine(expiry_date, datetime.min.time())

        now = datetime.now()
        days_to_expiry = (expiry - now).days + 1  # Add 1 to include today
        return max(days_to_expiry / 365.0, 0.0001)  # Minimum value to avoid division by zero

    


