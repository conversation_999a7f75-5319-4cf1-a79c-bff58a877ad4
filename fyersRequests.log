{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 00:45:09,916+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 00:45:25,503+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 00:47:12,596+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 00:47:12,739+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 00:47:12,832+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 00:47:13,000+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,225+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,337+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,479+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,596+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,699+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,844+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:13,947+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,049+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,151+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,265+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,377+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,477+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,633+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:14,877+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,016+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,139+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,258+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,405+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,515+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,632+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,734+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:15,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,069+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,180+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,283+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,390+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,498+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,592+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,693+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,798+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:16,900+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:17,018+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:17,124+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:17,234+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 00:47:17,385+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:22:50,209+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:22:50,345+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:22:50,448+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:22:50,563+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:25:09,200+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:25:09,326+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:25:09,423+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:25:09,579+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:29:33,150+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:29:33,292+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:29:33,390+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:29:33,503+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:29:33,727+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:31:56,272+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:31:56,407+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:31:56,510+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:31:56,624+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:31:56,847+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:32:45,602+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:32:46,031+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:32:46,977+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:32:47,189+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:32:47,415+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:43:58,074+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:43:58,193+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:43:58,300+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:43:58,449+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:43:58,713+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:51:41,532+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:51:41,648+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:51:41,757+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:51:41,888+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,132+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,261+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,570+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,679+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,802+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:42,909+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,023+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,140+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,245+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,363+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,481+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,586+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,703+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,813+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:43,932+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,036+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,145+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,248+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,362+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,475+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,590+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,838+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:44,941+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,050+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,195+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,300+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,410+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,545+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,661+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,767+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,887+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:45,996+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:46,116+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:51:46,227+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:52:44,714+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 01:52:44,886+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:52:45,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 01:52:45,225+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:45,680+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:45,984+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:46,173+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:46,400+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:46,711+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:47,184+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:47,486+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:47,644+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:47,822+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:47,951+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,108+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,247+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,389+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,678+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,808+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:48,935+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:49,065+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:49,206+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:49,334+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:49,547+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:49,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:49,895+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,050+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,190+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,332+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,485+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,640+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,780+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:50,930+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:51,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:51,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:51,516+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:51,689+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:51,864+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 01:52:52,046+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 02:01:09,698+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 02:01:09,834+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 02:01:09,948+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 02:01:10,062+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:10,277+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:10,385+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:10,516+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:10,612+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:10,721+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:10,876+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,086+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,206+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,301+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,408+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,517+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,624+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,736+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,841+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:11,946+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:12,053+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:12,282+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:12,640+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:13,007+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:13,445+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:13,881+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:14,312+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:14,685+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:15,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:15,801+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:15,912+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,020+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,136+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,247+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,343+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,451+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,560+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,660+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,764+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:16,866+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:01:17,035+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 02:07:05,165+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 02:07:05,304+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 02:07:05,402+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 02:07:05,528+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:05,758+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:05,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:05,967+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:06,084+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:06,211+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:06,313+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:06,409+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:06,522+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:06,775+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,045+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,148+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,282+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,422+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,525+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,638+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,749+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:07,970+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,078+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,172+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,267+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,380+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,488+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,601+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,723+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,848+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:08,976+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:09,114+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:09,234+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:09,413+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:09,567+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:09,729+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:09,873+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:10,034+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:10,174+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:07:10,312+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 02:11:43,557+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 02:11:43,679+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 02:11:43,810+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 02:11:43,932+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,184+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,285+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,387+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,498+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,594+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,694+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,842+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:44,950+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,059+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,190+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,408+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,548+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,688+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,833+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:45,931+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,040+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,140+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,246+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,377+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,590+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,698+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,802+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:46,900+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,028+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,175+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,290+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,403+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,504+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,606+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,733+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,875+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:47,982+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:48,097+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:48,202+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 02:11:48,298+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:01:29,976+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:01:30,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:01:30,218+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:01:30,385+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:30,687+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:30,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,015+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,131+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,253+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,373+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,504+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,777+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:31,920+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,049+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,260+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,373+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,499+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,626+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,755+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,862+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:32,983+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,108+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,272+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,451+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,599+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,713+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,837+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:33,959+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,122+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,243+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,370+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,482+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,653+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,771+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:34,877+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:35,004+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:35,155+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:35,267+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:01:35,392+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:12:05,156+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:12:05,316+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:12:05,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:12:05,594+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:05,846+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:05,991+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:06,136+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:06,326+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:06,476+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:06,598+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:06,737+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:06,886+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,008+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,109+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,223+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,349+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,462+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,618+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:07,887+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,010+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,157+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,294+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,444+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,573+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,743+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:08,858+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,151+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,280+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,435+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,603+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,705+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,827+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:09,949+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:10,071+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:10,191+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:10,315+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:10,425+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:10,543+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:12:23,186+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:12:23,328+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:12:23,444+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:12:23,594+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:23,879+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,010+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,120+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,268+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,396+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,549+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,669+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,815+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:24,939+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,075+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,195+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,333+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,475+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,593+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,713+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,823+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:25,969+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,084+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,212+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,318+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,501+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,625+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,752+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:26,909+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:27,070+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:27,232+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:27,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:27,545+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:27,745+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:27,891+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:28,009+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:28,150+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:28,272+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:28,383+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:28,568+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:12:28,672+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:31:26,377+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:31:26,576+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:31:27,051+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:31:27,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:28,289+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:28,891+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:29,488+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:30,085+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:30,369+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:30,478+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:30,672+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:30,855+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:31,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:31,298+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:31,439+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:31,591+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:31,802+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:31,917+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:32,117+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:32,319+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:32,527+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:32,720+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:32,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:33,104+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:33,319+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:33,516+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:33,756+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:31:33,934+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:32:58,746+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:32:59,058+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:32:59,262+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:32:59,570+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:32:59,877+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:00,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:00,291+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:00,592+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:00,791+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:01,091+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:01,306+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:01,571+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:01,721+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:01,922+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:02,223+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:02,423+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:02,591+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:02,839+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:03,152+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:03,451+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:03,653+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:03,855+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:04,075+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:04,272+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:04,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:04,887+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:05,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:33:05,206+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:40:26,135+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:40:26,441+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 13:40:26,650+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-06-27 13:40:26,862+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:41:37,620+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:41:37,750+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-06-27 13:41:37,907+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:43:22,689+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:43:22,829+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-06-27 13:43:22,996+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:43:43,170+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:43:43,474+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":400,"API":"/depth"},"timestamp":"2025-06-27 13:43:43,787+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-06-27 13:43:44,072+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:44:15,014+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:44:15,228+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":400,"API":"/depth"},"timestamp":"2025-06-27 13:44:15,515+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:44:15,729+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:45:28,431+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:45:28,742+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":400,"API":"/depth"},"timestamp":"2025-06-27 13:45:29,043+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:45:29,359+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:46:40,421+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:46:40,997+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:46:41,419+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:53:26,449+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 13:53:26,762+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/depth"},"timestamp":"2025-06-27 13:53:26,967+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 13:53:27,163+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 14:04:46,198+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 14:04:46,502+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 14:04:46,715+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 14:04:46,915+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:47,216+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:47,431+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:47,635+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:47,816+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:47,945+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:48,247+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:48,399+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:48,664+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:48,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:49,261+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:49,494+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:49,683+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:49,978+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:50,271+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:50,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:50,596+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:50,860+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:51,013+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:51,424+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:51,625+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:51,926+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:52,143+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:52,442+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:04:52,643+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 14:40:47,413+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 14:40:47,618+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 14:40:47,925+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 14:40:48,234+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:48,616+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:48,744+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:48,948+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:49,153+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:49,358+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:49,496+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:49,666+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:49,870+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:50,074+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:50,279+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:50,486+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:50,792+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:50,997+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:51,202+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:51,406+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:51,611+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:51,817+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:52,021+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:52,225+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:52,533+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:52,740+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:52,942+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:53,249+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 14:40:53,454+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 15:30:11,054+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 15:30:11,278+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 15:30:11,561+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 15:30:11,868+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:12,279+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:12,483+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:12,791+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:13,098+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:13,404+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:13,711+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:14,121+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:14,326+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:14,531+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:14,940+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:15,144+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:15,350+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:15,658+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:16,067+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:16,241+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:16,476+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:16,784+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:16,987+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:17,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:17,500+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:17,795+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:18,012+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:18,319+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 15:30:18,524+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 16:05:08,051+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 16:05:08,448+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/depth"},"timestamp":"2025-06-27 16:05:08,659+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-06-27 16:05:08,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 16:38:06,655+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 16:38:06,815+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 16:38:06,988+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 16:38:07,167+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:07,510+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:07,647+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:07,812+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:07,982+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:08,138+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:08,276+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:08,491+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:08,639+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:08,811+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:08,986+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:09,194+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:09,375+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:09,564+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:09,727+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:09,914+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:10,075+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:10,216+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:10,410+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:10,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:10,670+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:10,838+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:11,005+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:11,138+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 16:38:11,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 20:03:19,126+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 20:03:19,443+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 20:03:19,643+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 20:03:20,006+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:20,364+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:20,559+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:20,776+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:21,071+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:21,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:21,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:21,890+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:22,202+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:22,408+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:22,718+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:23,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:23,336+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:23,644+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:23,839+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:24,151+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:24,457+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:24,765+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:24,970+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:25,171+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:25,486+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:25,691+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:25,895+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:26,191+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 20:03:26,509+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 22:30:33,408+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 22:30:33,719+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 22:30:33,918+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 22:30:34,133+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:34,538+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:34,744+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:35,052+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:35,255+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:35,455+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:35,770+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:36,069+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:36,385+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:36,702+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:36,999+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:37,303+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:37,508+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:37,818+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:38,018+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:38,331+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:38,534+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:38,934+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:39,250+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:39,451+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:39,665+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:39,866+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:40,066+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:40,378+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:30:40,578+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 22:59:45,731+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 22:59:46,043+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 22:59:46,246+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 22:59:46,544+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:46,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:47,266+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:47,575+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:48,078+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:48,390+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:48,599+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:49,009+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:49,217+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:49,524+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:49,824+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:50,137+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:50,377+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:50,548+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:50,744+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:50,912+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:51,162+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:51,461+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:51,675+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:51,876+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:52,076+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:52,276+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:52,492+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:52,696+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 22:59:52,903+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 23:19:42,824+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-27 23:19:44,254+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 23:19:44,446+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-27 23:19:44,664+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:45,266+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:45,686+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:45,993+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:46,297+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:46,606+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:46,912+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:47,122+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:47,327+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:47,534+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:47,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:47,930+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:48,255+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:48,558+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:48,853+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:49,156+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:49,454+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:49,674+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:49,985+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:50,285+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:50,599+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:50,810+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:51,118+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:51,418+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-27 23:19:51,724+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:03:24,424+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:03:24,634+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:03:24,788+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:03:25,042+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:25,455+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:25,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:25,866+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:25,965+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:26,165+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:26,481+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:26,686+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:26,994+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:27,197+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:27,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:27,597+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:27,913+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:28,213+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:28,529+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:28,829+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:29,145+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:29,445+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:29,645+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:29,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:30,061+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:30,361+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:30,577+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:03:45,734+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:03:45,930+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:03:46,131+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:03:46,447+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:46,860+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:47,012+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:47,160+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:47,260+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:47,474+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:47,776+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:47,984+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:48,188+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:48,395+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:48,604+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:48,805+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:49,006+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:49,206+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:49,411+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:49,620+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:49,819+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:50,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:50,239+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:50,435+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:50,648+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:50,850+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:03:51,059+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:18:05,721+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:18:05,916+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:18:06,022+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:18:06,220+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:06,484+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:06,633+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:06,838+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:07,146+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:07,350+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:07,553+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:07,719+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:07,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:08,119+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:08,273+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:08,447+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:08,780+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:08,980+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:09,396+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:09,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:09,911+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:10,012+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:10,312+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:10,512+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:10,831+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:10,927+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:18:11,128+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:44:35,421+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 00:44:35,542+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:44:35,809+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 00:44:36,030+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:36,333+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:36,538+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:36,845+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:36,958+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:37,141+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:37,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:37,555+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:37,754+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:38,068+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:38,270+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:38,472+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:38,687+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:38,792+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:38,995+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:39,195+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:39,403+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:39,610+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:39,803+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:39,986+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:40,224+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:40,446+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 00:44:40,732+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 01:47:19,002+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 01:47:19,223+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 01:47:19,526+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 01:47:20,758+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:21,266+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:21,570+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:21,877+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:22,090+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:22,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:22,500+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:22,703+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:22,898+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:23,113+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:23,313+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:23,514+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:47:23,826+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 01:50:00,322+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 01:50:00,710+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 01:50:00,913+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 01:50:02,037+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:02,552+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:02,754+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:02,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:03,066+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:03,367+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:03,566+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:03,884+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:04,186+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:04,398+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:05,046+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:05,433+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:50:05,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 01:57:13,919+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 01:57:14,079+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 01:57:14,182+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 01:57:15,310+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:15,812+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:16,026+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:16,225+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:16,410+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:16,640+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:16,945+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:17,152+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:17,324+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:17,556+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:17,962+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:18,175+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 01:57:18,453+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 02:24:23,915+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-28 02:24:24,122+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 02:24:24,450+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-28 02:24:26,079+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:26,875+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:27,645+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:27,870+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:28,223+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:28,427+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:28,632+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:28,832+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:29,042+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:29,342+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:29,554+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:29,759+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-28 02:24:29,963+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:29:55,423+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:29:55,632+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:29:56,201+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:29:58,813+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:29:59,412+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:29:59,628+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:29:59,921+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:00,137+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:00,306+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:00,538+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:00,752+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:00,890+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:01,054+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:01,254+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:01,471+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:30:01,776+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:41:22,858+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:41:23,150+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/depth"},"timestamp":"2025-06-29 00:41:23,370+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-06-29 00:41:23,478+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:42:13,953+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:42:14,261+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/depth"},"timestamp":"2025-06-29 00:42:14,456+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:14,578+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:42:34,757+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:42:35,050+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:42:35,254+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:42:36,557+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:37,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:37,306+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:37,503+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:37,817+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:37,927+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:38,137+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:38,419+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:38,635+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:38,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:38,936+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:39,135+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 00:42:39,286+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:44:19,293+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 00:44:19,412+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:19,605+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:20,725+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:20,941+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:21,093+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:21,243+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:21,344+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:21,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:21,642+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:21,845+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:22,061+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:22,165+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:22,359+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:22,574+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:22,778+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:22,978+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:23,175+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:23,292+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:23,496+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:23,691+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:23,809+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:24,005+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:24,306+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:24,418+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:24,623+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:24,822+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:25,023+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:25,237+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:25,441+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:25,644+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:25,839+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:26,054+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:26,255+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:26,538+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:26,658+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:26,869+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:27,080+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:27,253+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:27,489+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:27,589+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:27,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:27,888+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:28,006+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:28,130+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 00:44:28,304+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:00:47,557+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:00:47,932+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:48,309+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:49,530+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:49,731+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:50,030+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:50,244+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:50,446+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:50,661+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:50,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:51,067+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:51,262+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:51,464+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:51,679+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:51,885+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:52,097+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:52,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:52,494+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:52,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:52,895+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:53,305+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:53,525+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:53,727+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:53,832+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:54,029+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:54,326+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:54,549+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:54,747+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:55,055+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:55,245+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:55,458+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:55,675+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:55,874+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:56,172+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:56,391+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:56,494+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:56,703+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:56,893+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:57,076+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:57,211+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:57,409+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:57,573+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:57,724+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:57,842+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:58,025+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:00:58,222+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:05:56,055+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:05:56,221+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:05:56,526+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:05:58,121+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:05:58,543+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:05:59,330+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:05:59,878+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:00,220+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:00,327+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:00,516+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:00,733+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:00,839+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:01,033+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:01,200+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:01,447+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:01,648+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:01,852+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,118+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,268+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,372+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,663+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,883+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:02,998+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:03,132+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:03,347+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:03,497+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:03,696+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:03,901+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:04,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:04,311+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:04,491+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:04,624+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:04,780+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:05,030+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:05,341+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:05,443+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:05,595+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:05,745+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:05,944+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:06,143+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:06,247+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:06,461+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:06,570+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:06,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:06:06,979+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:10:57,515+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:10:57,698+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:10:57,833+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:10:59,030+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:10:59,533+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:10:59,681+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:10:59,953+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:00,158+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:00,361+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:00,567+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:00,747+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:00,847+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:01,077+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:01,264+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:01,477+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:11:01,896+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:45:39,854+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:45:40,072+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:45:40,363+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:45:40,571+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:47:42,216+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:47:42,419+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:47:42,634+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:47:43,870+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:44,370+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:44,785+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:45,322+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:45,741+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:46,807+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:47,178+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:47,542+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:47,957+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:48,261+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:48,447+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:48,674+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 01:47:48,880+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:48:23,699+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:48:23,905+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:24,104+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:25,328+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:25,529+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:25,632+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:25,845+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:26,247+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:26,460+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:26,667+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:26,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:27,012+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:27,166+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:27,281+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:27,581+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:27,750+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:27,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:28,096+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:28,204+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:28,409+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:28,612+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:28,716+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:28,959+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:29,223+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:29,427+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:29,624+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:29,729+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:29,941+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:30,045+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:30,145+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:30,354+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:30,558+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:30,708+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:30,960+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:31,276+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:31,475+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:31,787+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:32,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:32,290+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:32,441+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:32,593+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:32,855+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:33,008+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:33,207+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:33,326+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:48:33,505+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:58:58,492+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 01:58:58,795+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:58:58,897+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:00,024+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:00,230+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:00,479+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:00,817+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:00,950+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:01,256+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:01,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:01,565+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:01,763+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:01,881+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,178+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,311+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,483+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,590+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,793+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:02,905+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:03,100+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:03,305+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:03,510+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:03,710+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:03,919+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:04,123+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:04,330+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:04,535+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:04,739+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:04,940+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:05,042+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:05,251+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:05,456+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:05,660+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:05,865+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:06,070+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:06,374+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:06,574+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:06,691+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:06,859+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:06,992+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:07,342+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:07,737+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:08,122+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:08,552+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 01:59:08,971+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:12:30,030+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:12:30,257+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:30,379+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:31,555+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:32,025+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:32,173+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:32,385+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:32,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:32,870+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:33,005+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:33,180+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:33,309+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:33,614+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:33,821+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:34,018+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:12:34,202+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:12:48,157+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:12:48,340+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:48,466+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:49,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:49,793+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:50,088+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:50,464+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:51,071+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:51,498+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:51,888+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:52,316+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:52,719+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:53,094+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:53,566+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:53,736+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:53,982+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:54,150+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:54,400+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:54,505+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:54,609+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:54,711+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:54,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:55,117+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:55,315+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:55,531+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:55,828+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,030+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,215+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,317+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,449+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,648+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,759+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:56,965+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:57,276+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:57,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:57,676+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:57,783+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:58,079+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:58,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:58,495+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:58,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:58,814+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:58,932+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:59,097+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:59,216+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:12:59,409+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:21:39,111+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:21:39,309+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:21:39,496+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:21:40,626+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:41,158+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:41,265+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:41,574+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:41,773+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:41,982+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:42,155+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:42,388+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:42,594+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:42,709+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:42,905+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:43,012+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:21:43,110+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:21:59,331+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:21:59,701+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:21:59,894+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,070+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,241+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,358+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,644+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,768+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:01,942+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:02,263+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:02,454+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:02,568+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:02,771+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:02,971+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:03,170+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:03,370+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:03,537+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:03,784+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:03,970+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:04,170+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:04,303+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:04,503+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:04,704+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:04,818+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:05,083+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:05,303+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:05,485+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:05,635+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:05,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:06,047+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:06,251+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:06,450+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:06,651+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:06,932+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:07,069+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:07,267+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:07,466+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:07,683+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:07,785+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:07,900+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:08,083+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:08,199+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:08,498+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:08,633+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:22:08,914+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:31:59,974+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:32:00,180+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:32:00,383+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:32:01,514+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:02,029+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:02,230+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:02,332+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:02,592+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:02,748+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:02,943+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:03,192+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:03,362+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:03,568+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:03,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:03,976+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:32:04,082+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:32:26,294+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:32:26,493+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:32:26,594+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:32:27,925+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:33:08,794+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:33:09,105+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:33:10,322+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:33:10,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:34:00,406+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:34:00,604+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:34:02,036+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:34:02,450+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:36:47,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:36:47,221+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:36:48,269+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:36:48,438+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:44:01,297+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:44:01,510+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:44:01,683+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:44:02,957+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:03,561+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:03,663+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:03,969+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:04,276+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:04,376+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:04,577+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:04,789+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:04,989+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:05,127+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:05,388+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:05,507+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:05,643+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:44:21,018+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:44:21,430+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:44:21,686+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:44:23,101+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:23,573+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:23,726+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:23,939+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:24,044+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:24,192+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:24,439+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:24,655+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:24,762+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:24,892+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:25,041+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:25,157+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:44:25,439+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:44:57,907+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:44:58,141+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:44:59,306+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:44:59,472+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:59:21,895+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:59:22,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:59:22,318+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:59:23,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:24,044+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:24,258+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:24,376+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:24,561+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:24,774+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:24,973+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:25,176+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:25,375+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:25,493+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:25,695+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:25,798+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:26,104+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:59:54,884+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 02:59:55,190+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:59:56,313+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 02:59:56,513+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:56,928+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:57,127+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:57,329+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:57,532+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:57,649+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:57,847+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,044+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,247+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,364+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,466+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,569+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:58,976+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:59,080+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:59,277+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:59,390+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:59,690+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 02:59:59,877+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:00,097+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:00,210+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:00,509+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:00,809+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,010+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,128+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,244+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,425+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,625+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,742+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:01,925+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:02,034+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:02,157+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:02,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:00:29,687+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:00:29,851+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:00:31,028+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:00:31,230+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:31,732+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:31,949+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:32,251+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:32,463+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:32,665+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:32,847+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:33,077+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:33,384+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:33,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:33,744+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:33,896+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:34,103+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:34,407+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:34,613+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:34,816+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:34,998+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:35,213+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:35,432+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:35,649+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:35,842+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:36,136+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:36,355+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:36,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:36,945+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:37,173+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:37,278+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:37,475+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:37,674+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:37,879+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:37,993+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:38,195+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:00:38,299+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:02:28,363+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:02:28,528+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:02:29,611+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:02:29,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:30,328+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:30,528+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:30,725+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:30,862+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:30,956+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:31,061+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:31,350+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:31,547+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:31,726+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:31,967+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:32,699+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:33,325+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:33,753+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:34,163+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:34,760+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:35,358+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:35,551+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:35,654+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:35,757+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:35,858+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:36,164+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:36,352+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:36,470+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:36,764+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:36,984+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:37,280+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:37,484+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:37,797+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:37,999+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:38,200+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:38,416+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:38,616+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:02:38,739+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:17:48,982+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:17:49,174+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:17:50,496+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:17:50,812+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:51,328+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:51,543+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:51,643+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:51,850+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:52,154+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:52,354+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:52,522+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:52,769+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:53,078+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:53,371+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:53,587+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:53,794+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:53,987+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:54,203+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:54,405+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:54,511+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:54,707+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:54,911+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:55,053+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:55,174+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:55,425+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:55,543+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:55,738+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:55,846+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:56,040+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:56,241+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:56,456+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:56,737+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:56,967+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:57,153+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:57,371+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:57,595+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:57,886+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:17:58,198+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:21:36,462+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:21:36,624+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:21:37,736+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:21:37,952+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:41:17,426+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:41:17,545+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:41:18,750+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:41:18,965+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:42:49,897+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:42:50,100+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:42:50,301+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:42:50,614+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:43:06,587+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:43:06,795+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:43:07,199+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:43:07,292+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:47:06,508+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:47:06,718+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:47:07,850+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:47:08,050+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:08,476+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:08,665+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:08,773+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:08,875+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:09,080+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:09,281+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:09,488+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:09,693+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:09,996+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:10,307+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:10,401+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:10,708+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:11,123+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:11,284+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:11,430+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:11,532+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:11,839+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:12,048+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:12,244+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:12,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:12,565+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:12,760+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:12,960+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:13,273+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:13,477+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:13,785+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:14,039+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:14,193+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:14,348+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:14,497+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:14,814+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:15,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:15,213+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:47:15,429+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:51:34,560+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:51:34,785+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:51:35,840+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:51:36,044+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:36,606+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:36,971+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:37,173+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:37,375+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:37,668+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:37,888+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:38,092+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:38,287+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:38,439+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:38,567+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:38,691+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:38,888+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,039+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,219+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,420+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,624+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,730+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,825+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:39,936+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:40,345+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:40,550+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:40,753+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:41,055+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:41,165+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:41,470+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:41,572+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:41,771+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:41,969+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:42,120+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:42,287+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:42,701+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:42,901+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:43,101+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:51:43,418+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:54:35,660+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:54:35,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:54:37,093+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:54:37,388+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:37,796+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:38,011+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:38,125+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:38,316+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:38,525+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:38,730+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:38,833+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:39,026+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:39,341+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:39,448+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:39,641+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:40,180+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:40,803+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:41,437+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:41,873+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:42,270+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:42,841+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:43,167+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:43,337+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:43,538+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:43,851+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:44,054+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:44,358+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:44,565+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:44,772+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:44,874+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:45,172+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:45,483+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:45,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:45,705+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:45,886+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:54:46,006+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:58:42,264+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 03:58:42,445+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:58:43,667+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 03:58:43,875+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:44,289+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:44,389+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:44,590+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:44,786+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:45,102+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:45,316+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:45,430+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:45,618+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:45,818+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:46,019+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:46,234+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:46,435+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:46,748+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,048+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,251+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,366+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,566+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,673+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,874+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:47,980+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:48,084+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:48,286+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:48,386+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:48,549+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:48,783+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:48,997+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:49,199+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:49,309+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:49,612+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:49,817+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:50,014+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:50,230+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:50,528+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 03:58:50,641+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:03:07,561+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:03:07,765+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:03:08,898+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:03:09,097+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:09,564+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:09,910+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:10,129+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:10,329+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:10,593+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:10,744+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:11,051+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:11,244+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:11,461+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:11,564+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:11,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:11,871+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:12,096+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:12,479+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:12,886+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:13,304+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:13,698+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:14,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:14,515+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:14,895+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:15,466+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:15,657+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:15,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:15,956+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:16,158+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:16,372+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:16,525+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:16,679+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:16,785+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:16,988+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:17,191+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:17,398+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:17,605+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:03:17,803+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:10:15,815+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:10:16,011+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:10:16,226+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:10:16,431+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:30:14,726+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:30:14,945+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:30:16,176+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:30:16,297+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:42:49,558+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:42:49,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:42:50,890+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:42:51,186+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:53:56,319+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:53:56,505+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:53:57,623+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:53:57,838+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 04:53:58,339+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:59:15,376+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 04:59:15,583+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:59:17,077+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 04:59:17,707+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 16:03:42,063+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 16:03:42,244+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 16:03:42,346+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 16:03:43,550+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 16:03:44,045+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 16:03:44,263+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 18:32:10,930+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 18:32:11,236+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 18:32:11,440+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 18:32:12,515+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 18:32:13,181+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 18:47:42,282+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 18:47:42,589+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 18:47:42,780+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 18:47:43,818+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:08:06,127+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:08:06,404+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:08:06,609+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:08:07,634+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:08,145+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:08,350+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:08,461+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:08,657+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:08,965+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:09,170+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:09,476+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:09,682+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:09,885+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:10,091+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:10,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:08:10,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:08:43,475+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:08:43,681+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:08:43,794+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:08:45,022+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:09:02,419+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:09:02,726+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:09:03,854+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:09:03,992+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:04,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:04,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:05,081+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:05,231+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:05,450+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:05,593+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:05,798+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:05,993+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:06,104+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:06,310+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:06,514+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:06,702+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:06,876+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,013+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,128+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,333+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,743+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,846+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:07,949+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:08,153+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:08,325+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:08,460+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:08,767+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:08,972+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:09,084+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:09,279+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:09,485+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:09,654+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:09,891+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:10,268+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:10,655+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:09:11,294+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:19:23,159+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:19:23,299+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:19:24,596+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:19:25,207+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:21:39,686+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:21:39,950+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:21:42,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:21:42,802+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:22:06,925+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:22:07,130+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:22:08,256+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:22:08,462+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:38:51,932+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:38:52,417+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:38:53,649+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:38:53,852+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:54,261+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:54,470+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:54,670+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:54,978+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:55,184+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:55,387+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:55,489+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:55,784+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:56,002+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:56,104+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:56,309+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:56,513+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:56,616+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:38:56,821+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:42:58,491+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:42:58,644+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:42:59,721+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:43:00,028+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:00,539+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:00,744+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:01,051+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:01,358+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:01,563+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:01,769+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:01,973+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:02,178+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:02,383+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:02,587+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:02,749+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:02,895+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:03,002+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:03,201+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:03,408+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:03,611+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:03,817+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:04,110+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:04,225+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:04,374+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:04,533+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:04,737+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:05,045+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:05,250+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:05,455+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:05,659+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:05,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:05,967+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:06,173+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:06,329+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:06,479+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:06,683+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:43:46,974+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:43:47,235+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:43:48,258+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:43:48,566+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:48,976+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:49,180+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:49,285+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:49,590+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:49,898+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:50,065+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:50,307+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:50,512+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:50,718+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:50,838+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:51,024+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:51,228+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:51,639+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:51,843+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:52,151+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:52,354+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:52,560+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:52,765+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:52,970+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:53,174+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:53,378+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:53,481+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:53,686+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:54,097+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:54,300+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:54,506+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:54,710+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:54,914+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:55,120+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:55,210+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:55,428+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:43:55,632+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:46:55,570+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:46:55,861+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:46:57,047+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:46:57,251+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:57,703+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:57,909+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:58,011+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:58,218+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:58,523+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:58,830+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:58,953+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:59,239+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:59,547+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:59,649+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:46:59,956+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:00,161+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:00,366+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:00,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:00,878+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:01,185+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:01,390+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:01,697+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:01,902+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:02,107+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:02,415+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:02,619+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:02,926+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:03,131+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:03,410+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:03,540+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:03,745+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:04,053+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:04,257+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:04,564+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:04,771+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:47:04,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:49:20,044+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:49:20,314+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:49:21,374+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:49:21,580+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:22,077+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:22,194+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:22,440+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:22,603+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:22,809+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:23,013+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:23,218+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:23,423+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:23,628+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:23,833+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:24,140+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:24,344+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:24,550+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:24,766+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:24,959+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:25,163+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:25,368+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:25,675+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:25,779+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:25,983+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:26,111+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:26,233+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:26,392+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:26,597+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:26,802+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:27,007+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:27,295+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:27,416+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:27,519+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:27,685+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:27,928+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:49:28,133+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:51:07,429+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-29 19:51:07,669+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:51:07,773+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-29 19:51:09,000+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:09,512+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:09,614+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:09,719+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:09,922+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:10,146+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:10,323+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:10,536+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:10,740+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:10,945+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:11,150+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:11,355+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-06-29 19:51:11,461+0530","service":"FyersAPIRequest"}
