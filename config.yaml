# General Settings
general:
  env_path: 'C:/Users/<USER>/Desktop/Python/.env'
  output_dir: 'reports'

# Trading Symbols
symbols:
  - 'NIFTY'
  #- 'BANKNIFTY'

# Pivot Point Settings
pivot_point:
  calculation_type: 'WEEKLY' # 'DAILY', 'WEEKLY', 'MONTHLY'

# Timeframe Settings
timeframe:
  interval: 1
  days_to_fetch: 15

# Options Settings
options:
  expiry_type: 'MONTHLY'

# Options Filter Settings
options_filter:
  min_volume: 1000
  min_price: 2.0
  min_delta: 0.30
  max_delta: 0.65

# Strategy Execution Settings
strategy_execution:
  analysis_time: '09:45'
  closing_time: '15:06'
  lot_sizes:
    NIFTY: 50
    BANKNIFTY: 15
    FINNIFTY: 40